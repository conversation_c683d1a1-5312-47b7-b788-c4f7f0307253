*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
html,body{
    font-family: 'Inter', sans-serif;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
}
#main-wrapper{
    height: 100%;
    width: 100%;
}
.login-wrapper{
    min-height: 100vh;
    width: 100%;
    background: #edffed;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}
.login-card{
    border-radius: 32px;
    padding: 0;
    background: #fff;
    max-width: 991px;
    width: 100%;
    margin: 0 auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}
/* card left side   */

.login-card-left{
    padding: 20px;
}

/* Form styling */
.form-control {
    padding: 10px 15px;
}
.form-control:focus {
    border-color: none;
    box-shadow: none;
}
.login-card-left .btn-primary {
    padding: 10px;
    border-radius: 8px;
}
.login-card-left .btn-outline-secondary {
    border-radius: 8px;
    padding: 10px;
}

/* Divider line */
.divider .line {
    height: 1px;
    background-color: #e0e0e0;
}
/* card right side   */
.login-card-right{
    width: 100%;
    height: 100%;
    padding: 20px;
    overflow: hidden;
}
.login-card-right img{
    border-radius: 20px;
}
.object-fit-cover {
    object-fit: cover;
}