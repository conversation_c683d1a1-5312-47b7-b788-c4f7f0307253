*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
html,body{
    font-family: 'Inter', sans-serif;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
}
#main-wrapper{
    height: 100%;
    width: 100%;
}
.container-fluid{
    padding: 0;
}

.login-2-wrapper{
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.login-card-2-left{
    min-height: 100vh;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 0px 20px 20px 0px;
}
.social-login img {
    transition: all 0.3s ease;
    border-radius: 10px;
    padding: 8px;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.8);
}
.social-login img:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Creative Input Styling */
.input-group {
    position: relative;
    margin-bottom: 20px;
}

.input-group input {
    width: 100%;
    padding: 15px 20px 15px 50px;
    border: 2px solid #e1e5e9;
    border-radius: 15px;
    font-size: 16px;
    background: #f8f9fa;
    transition: all 0.3s ease;
    outline: none;
}

.input-group input:focus {
    border-color: #667eea;
    background: #fff;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.input-group input:focus + .input-label {
    color: #667eea;
    transform: translateY(-25px) scale(0.8);
}

.input-group input:not(:placeholder-shown) + .input-label {
    transform: translateY(-25px) scale(0.8);
    color: #667eea;
}

.input-label {
    position: absolute;
    left: 50px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 16px;
    pointer-events: none;
    transition: all 0.3s ease;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.input-icon {
    position: absolute;
    left: 18px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 18px;
    transition: all 0.3s ease;
}

.input-group input:focus ~ .input-icon {
    color: #667eea;
    transform: translateY(-50%) scale(1.1);
}

/* Creative Checkbox Styling */
.custom-checkbox {
    position: relative;
    display: inline-block;
    margin-bottom: 1rem;
}

.custom-checkbox input[type="checkbox"] {
    opacity: 0;
    position: absolute;
    width: 0;
    height: 0;
}

.checkbox-slider {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 25px;
    background: #ccc;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 10px;
}

.checkbox-slider:before {
    content: "";
    position: absolute;
    top: 2px;
    left: 2px;
    width: 21px;
    height: 21px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.custom-checkbox input:checked + .checkbox-slider {
    background: linear-gradient(45deg, #667eea, #764ba2);
}

.custom-checkbox input:checked + .checkbox-slider:before {
    transform: translateX(25px);
}

.checkbox-label {
    font-size: 14px;
    color: #495057;
    cursor: pointer;
    user-select: none;
    display: inline-flex;
    align-items: center;
}


/* Welcome Message */
.welcome-message h4 {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* Social Icons Hover Effect */

/* Full Width Slider Section Styling */
.slider-section {
    height: 100vh;
    position: relative;
    overflow: hidden;
    width: 100%;
}

.slider-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transition: all 1s ease-in-out;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
}

.slide.active {
    opacity: 1;
    visibility: visible;
}

.slide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
    z-index: 1;
}

.slide-content {
    position: relative;
    z-index: 2;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px;
    text-align: center;
}

.slide-text {
    max-width: 800px;
    color: white;
    transform: translateY(50px);
    opacity: 0;
    transition: all 0.8s ease 0.3s;
}

.slide.active .slide-text {
    transform: translateY(0);
    opacity: 1;
}

.slide-text h2 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 30px;
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transform: translateY(30px);
    opacity: 0;
    transition: all 0.8s ease 0.5s;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.slide.active .slide-text h2 {
    transform: translateY(0);
    opacity: 1;
}

.slide-text p {
    font-size: 1.3rem;
    line-height: 1.8;
    margin-bottom: 40px;
    color: rgba(255, 255, 255, 0.95);
    transform: translateY(30px);
    opacity: 0;
    transition: all 0.8s ease 0.7s;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.slide.active .slide-text p {
    transform: translateY(0);
    opacity: 1;
}

.slide-features {
    display: flex;
    justify-content: center;
    gap: 40px;
    transform: translateY(30px);
    opacity: 0;
    transition: all 0.8s ease 0.9s;
    flex-wrap: wrap;
}

.slide.active .slide-features {
    transform: translateY(0);
    opacity: 1;
}

.feature {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 30px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 50px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    cursor: pointer;
}

.feature:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.feature i {
    color: #ffd700;
    font-size: 1.5rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.feature span {
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Slider Navigation */
.slider-nav {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
    z-index: 3;
}

.nav-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.nav-dot:hover {
    background: rgba(255, 255, 255, 0.7);
    transform: scale(1.2);
}

.nav-dot.active {
    background: white;
    transform: scale(1.3);
}

.nav-dot.active::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

/* Button Styling */
.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    border-radius: 15px;
    padding: 15px 30px;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn-primary:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover:before {
    left: 100%;
}

/* Divider Styling */
.line {
    height: 1px;
    background: linear-gradient(90deg, transparent, #ddd, transparent);
}

/* Responsive Design for Slider */
@media (max-width: 768px) {
    .slide-text h2 {
        font-size: 2.5rem;
    }

    .slide-text p {
        font-size: 1.1rem;
    }

    .slide-features {
        flex-direction: column;
        gap: 20px;
        align-items: center;
    }

    .slide-content {
        padding: 40px 20px;
    }
}
