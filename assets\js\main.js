// Full Width Slider Animation JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const slides = document.querySelectorAll('.slide');
    const navDots = document.querySelectorAll('.nav-dot');
    let currentSlide = 0;
    let slideInterval;

    // Function to show a specific slide
    function showSlide(index) {
        // Remove active class from all slides and nav dots
        slides.forEach(slide => slide.classList.remove('active'));
        navDots.forEach(dot => dot.classList.remove('active'));

        // Set background image for the current slide
        const currentSlideElement = slides[index];
        const bgImage = currentSlideElement.getAttribute('data-bg');
        if (bgImage) {
            currentSlideElement.style.backgroundImage = `url(${bgImage})`;
        }

        // Add active class to current slide and nav dot
        slides[index].classList.add('active');
        navDots[index].classList.add('active');

        currentSlide = index;
    }

    // Function to go to next slide
    function nextSlide() {
        currentSlide = (currentSlide + 1) % slides.length;
        showSlide(currentSlide);
    }

    // Function to go to previous slide
    function prevSlide() {
        currentSlide = (currentSlide - 1 + slides.length) % slides.length;
        showSlide(currentSlide);
    }

    // Auto-play slider
    function startSlider() {
        slideInterval = setInterval(nextSlide, 5000); // Change slide every 5 seconds
    }

    // Stop auto-play
    function stopSlider() {
        clearInterval(slideInterval);
    }

    // Add click event listeners to navigation dots
    navDots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            stopSlider();
            showSlide(index);
            startSlider(); // Restart auto-play after manual navigation
        });
    });

    // Add hover events to pause/resume auto-play
    const sliderContainer = document.querySelector('.slider-container');
    if (sliderContainer) {
        sliderContainer.addEventListener('mouseenter', stopSlider);
        sliderContainer.addEventListener('mouseleave', startSlider);
    }

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowLeft') {
            stopSlider();
            prevSlide();
            startSlider();
        } else if (e.key === 'ArrowRight') {
            stopSlider();
            nextSlide();
            startSlider();
        }
    });

    // Touch/swipe support for mobile
    let touchStartX = 0;
    let touchEndX = 0;

    if (sliderContainer) {
        sliderContainer.addEventListener('touchstart', (e) => {
            touchStartX = e.changedTouches[0].screenX;
        });

        sliderContainer.addEventListener('touchend', (e) => {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        });
    }

    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;

        if (Math.abs(diff) > swipeThreshold) {
            stopSlider();
            if (diff > 0) {
                nextSlide(); // Swipe left - next slide
            } else {
                prevSlide(); // Swipe right - previous slide
            }
            startSlider();
        }
    }

    // Initialize all background images and start slider
    if (slides.length > 0) {
        // Set background images for all slides
        slides.forEach((slide, index) => {
            const bgImage = slide.getAttribute('data-bg');
            if (bgImage) {
                slide.style.backgroundImage = `url(${bgImage})`;
            }
        });

        showSlide(0);
        startSlider();
    }

    // Intersection Observer for slide animations when in viewport
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const slideObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe all slide elements
    slides.forEach(slide => {
        slideObserver.observe(slide);
    });
});

// Additional animations for enhanced user experience
document.addEventListener('DOMContentLoaded', function() {
    // Add stagger animation to features
    const features = document.querySelectorAll('.feature');
    features.forEach((feature, index) => {
        feature.style.animationDelay = `${index * 0.1}s`;
    });

    // Parallax effect for background images
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const activeSlide = document.querySelector('.slide.active');
        if (activeSlide) {
            const rate = scrolled * -0.5;
            activeSlide.style.transform = `translateY(${rate}px)`;
        }
    });
});